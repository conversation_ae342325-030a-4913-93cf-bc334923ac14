/**
 * 环境配置使用示例
 * 展示如何在项目中正确使用环境变量
 */

import { 
  // 基础环境信息
  getAppEnv, 
  getAppTitle, 
  getPort,
  
  // 环境判断
  isDevelopment, 
  isTest, 
  isStaging, 
  isProduction,
  
  // API 相关
  getBaseURL, 
  getApiURL, 
  getApiTimeout,
  
  // 资源相关
  getAssetsUrl, 
  getWebURL, 
  getJumpURL, 
  getGcashShopURL,
  
  // 功能开关
  isMockEnabled, 
  isDevtoolsEnabled, 
  isConsoleEnabled, 
  isVConsoleEnabled,
  
  // 构建相关
  isDropConsole, 
  getBuildCompress,
  
  // 环境配置对象
  envConfig,
  
  // 工具函数
  printEnvInfo, 
  getEnvDisplayName, 
  isLocalDev, 
  getFullEnvInfo
} from './env';

// ==================== 使用示例 ====================

/**
 * 示例1: 基础使用
 */
export function basicUsageExample() {
  // 获取当前环境
  const currentEnv = getAppEnv();
  console.log('当前环境:', currentEnv);
  
  // 获取应用标题
  const title = getAppTitle();
  document.title = title;
  
  // 获取跳转URL
  const jumpUrl = getJumpURL();
  console.log('跳转URL:', jumpUrl);
}

/**
 * 示例2: 环境判断
 */
export function environmentCheckExample() {
  if (isDevelopment()) {
    console.log('开发环境特有逻辑');
    // 开发环境特有的代码
  }
  
  if (isProduction()) {
    console.log('生产环境特有逻辑');
    // 生产环境特有的代码
  }
  
  // 或者使用 switch
  switch (getAppEnv()) {
    case 'development':
      // 开发环境逻辑
      break;
    case 'test':
      // 测试环境逻辑
      break;
    case 'staging':
      // 预发环境逻辑
      break;
    case 'production':
      // 生产环境逻辑
      break;
  }
}

/**
 * 示例3: API 配置
 */
export function apiConfigExample() {
  // 创建 axios 实例
  const apiConfig = {
    baseURL: getApiURL(),
    timeout: getApiTimeout(),
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  console.log('API 配置:', apiConfig);
}

/**
 * 示例4: 功能开关
 */
export function featureFlagExample() {
  // 根据环境变量决定是否启用某些功能
  if (isMockEnabled()) {
    console.log('启用 Mock 数据');
    // 加载 Mock 数据
  }
  
  if (isDevtoolsEnabled()) {
    console.log('启用开发者工具');
    // 加载开发者工具
  }
  
  if (isVConsoleEnabled()) {
    console.log('启用 VConsole');
    // 动态加载 VConsole
    import('vconsole').then(VConsole => {
      new VConsole.default();
    });
  }
}

/**
 * 示例5: 使用环境配置对象
 */
export function envConfigObjectExample() {
  // 使用完整的环境配置对象
  console.log('完整环境配置:', envConfig);
  
  // 解构使用
  const { baseURL, apiTimeout, enableMock } = envConfig;
  
  // 传递给其他模块
  const config = {
    api: {
      baseURL: envConfig.baseURL,
      timeout: envConfig.apiTimeout,
    },
    features: {
      mock: envConfig.enableMock,
      devtools: envConfig.enableDevtools,
    }
  };
  
  return config;
}

/**
 * 示例6: 资源URL处理
 */
export function resourceUrlExample() {
  // 获取静态资源URL
  const assetsUrl = getAssetsUrl();
  
  // 构建完整的资源路径
  const logoUrl = `${assetsUrl}images/logo.png`;
  const iconUrl = `${assetsUrl}icons/favicon.ico`;
  
  console.log('Logo URL:', logoUrl);
  console.log('Icon URL:', iconUrl);
  
  // 在组件中使用
  const imageElement = document.createElement('img');
  imageElement.src = logoUrl;
  
  return { logoUrl, iconUrl };
}

/**
 * 示例7: 跳转功能
 */
export function navigationExample() {
  const jumpUrl = getJumpURL();
  
  // 页面跳转
  function redirectToMain() {
    window.location.href = jumpUrl;
  }
  
  // 新窗口打开
  function openInNewTab() {
    window.open(jumpUrl, '_blank');
  }
  
  return { redirectToMain, openInNewTab };
}

/**
 * 示例8: 调试信息
 */
export function debugInfoExample() {
  // 打印环境信息（仅在启用控制台时）
  printEnvInfo();
  
  // 获取环境显示名称
  const envName = getEnvDisplayName();
  console.log('环境名称:', envName);
  
  // 检查是否为本地开发
  if (isLocalDev()) {
    console.log('本地开发环境');
  }
  
  // 获取完整环境信息
  const fullInfo = getFullEnvInfo();
  console.log('完整环境信息:', fullInfo);
}

/**
 * 示例9: 条件渲染
 */
export function conditionalRenderingExample() {
  // 根据环境显示不同的内容
  function getEnvironmentBadge() {
    const env = getAppEnv();
    const envName = getEnvDisplayName();
    
    if (env === 'production') {
      return null; // 生产环境不显示徽章
    }
    
    return {
      text: envName,
      color: {
        development: '#4CAF50',
        test: '#FF9800', 
        staging: '#2196F3'
      }[env] || '#9E9E9E'
    };
  }
  
  return getEnvironmentBadge();
}

/**
 * 示例10: 错误处理
 */
export function errorHandlingExample() {
  try {
    // 安全地获取环境变量
    const config = {
      baseURL: getBaseURL() || 'https://fallback.example.com',
      timeout: getApiTimeout() || 30000,
      assetsUrl: getAssetsUrl() || '/assets/',
    };
    
    return config;
  } catch (error) {
    console.error('环境配置获取失败:', error);
    
    // 返回默认配置
    return {
      baseURL: 'https://fallback.example.com',
      timeout: 30000,
      assetsUrl: '/assets/',
    };
  }
}
