/**
 * 环境变量测试脚本
 * 在浏览器控制台中运行此脚本来测试环境变量加载情况
 */

// 测试原始环境变量
console.group('🔍 原始环境变量测试');
console.log('VITE_APP_TITLE:', import.meta.env.VITE_APP_TITLE);
console.log('VITE_APP_ENV:', import.meta.env.VITE_APP_ENV);
console.log('VITE_PORT:', import.meta.env.VITE_PORT);
console.log('VITE_BASE_URL:', import.meta.env.VITE_BASE_URL);
console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
console.log('VITE_JUMP_URL:', import.meta.env.VITE_JUMP_URL);
console.log('VITE_ASSETS_URL:', import.meta.env.VITE_ASSETS_URL);
console.log('VITE_WEB_URL:', import.meta.env.VITE_WEB_URL);
console.log('VITE_ENABLE_CONSOLE:', import.meta.env.VITE_ENABLE_CONSOLE);
console.log('VITE_ENABLE_DEVTOOLS:', import.meta.env.VITE_ENABLE_DEVTOOLS);
console.log('VITE_ENABLE_MOCK:', import.meta.env.VITE_ENABLE_MOCK);
console.log('完整的 import.meta.env:', import.meta.env);
console.groupEnd();

// 测试工具函数（需要先导入）
import { 
  getJumpURL, 
  getBaseURL, 
  getApiURL, 
  getAssetsUrl,
  getWebURL,
  envConfig,
  printEnvInfo 
} from './utils/env.js';

console.group('🛠️ 工具函数测试');
console.log('getJumpURL():', getJumpURL());
console.log('getBaseURL():', getBaseURL());
console.log('getApiURL():', getApiURL());
console.log('getAssetsUrl():', getAssetsUrl());
console.log('getWebURL():', getWebURL());
console.groupEnd();

console.group('📦 环境配置对象测试');
console.log('envConfig:', envConfig);
console.groupEnd();

// 打印详细环境信息
printEnvInfo();

// 检查空值
console.group('⚠️ 空值检查');
const emptyVars = [];
const envVars = {
  'VITE_JUMP_URL': import.meta.env.VITE_JUMP_URL,
  'VITE_BASE_URL': import.meta.env.VITE_BASE_URL,
  'VITE_API_URL': import.meta.env.VITE_API_URL,
  'VITE_ASSETS_URL': import.meta.env.VITE_ASSETS_URL,
  'VITE_WEB_URL': import.meta.env.VITE_WEB_URL,
};

Object.entries(envVars).forEach(([name, value]) => {
  if (!value || value === '') {
    emptyVars.push(name);
  }
});

if (emptyVars.length > 0) {
  console.warn('以下环境变量为空:', emptyVars);
} else {
  console.log('✅ 所有重要环境变量都已正确加载');
}
console.groupEnd();

// 导出测试结果
window.envTestResult = {
  rawEnvVars: envVars,
  utilityFunctions: {
    getJumpURL: getJumpURL(),
    getBaseURL: getBaseURL(),
    getApiURL: getApiURL(),
    getAssetsUrl: getAssetsUrl(),
    getWebURL: getWebURL(),
  },
  envConfig: envConfig,
  emptyVars: emptyVars,
  allGood: emptyVars.length === 0
};

console.log('🎯 测试结果已保存到 window.envTestResult');
