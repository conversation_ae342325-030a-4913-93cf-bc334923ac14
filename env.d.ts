// src/env.d.ts
/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

export interface ImportMetaEnv {
  // 应用基础配置
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_ENV: string;
  readonly VITE_PORT: number;

  // API 相关配置
  readonly VITE_BASE_URL: string;
  readonly VITE_API_URL: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_PROXY_TARGET: string;

  // 资源相关配置
  readonly VITE_ASSETS_URL: string;
  readonly VITE_WEB_URL: string;
  readonly VITE_JUMP_URL: string;
  readonly VITE_GCASH_SHOP_URL: string;

  // 功能开关
  readonly VITE_ENABLE_MOCK: string;
  readonly VITE_ENABLE_DEVTOOLS: string;
  readonly VITE_ENABLE_CONSOLE: string;
  readonly VITE_ENABLE_VCONSOLE: string;

  // 构建相关配置
  readonly VITE_DROP_CONSOLE: string;
  readonly VITE_BUILD_COMPRESS: string;
  readonly VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE: string;

  // 代理配置
  readonly VITE_PROXY: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// SVG 模块类型声明
declare module "*.svg" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent;
  export default component;
}
