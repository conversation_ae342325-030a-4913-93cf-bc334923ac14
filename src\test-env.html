<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Variables Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .env-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        .env-name { font-weight: bold; color: #333; }
        .env-value { color: #666; margin-left: 10px; }
        .empty { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Environment Variables Test</h1>
    <div id="env-list"></div>

    <script type="module">
        import { getJumpURL, getBaseURL, getApiURL, envConfig, printEnvInfo } from './utils/env.js';

        // 打印环境信息到控制台
        printEnvInfo();

        // 测试环境变量
        const envVars = {
            'VITE_APP_TITLE': import.meta.env.VITE_APP_TITLE,
            'VITE_APP_ENV': import.meta.env.VITE_APP_ENV,
            'VITE_PORT': import.meta.env.VITE_PORT,
            'VITE_BASE_URL': import.meta.env.VITE_BASE_URL,
            'VITE_API_URL': import.meta.env.VITE_API_URL,
            'VITE_JUMP_URL': import.meta.env.VITE_JUMP_URL,
            'VITE_ASSETS_URL': import.meta.env.VITE_ASSETS_URL,
            'VITE_WEB_URL': import.meta.env.VITE_WEB_URL,
            'VITE_ENABLE_CONSOLE': import.meta.env.VITE_ENABLE_CONSOLE,
            'VITE_ENABLE_DEVTOOLS': import.meta.env.VITE_ENABLE_DEVTOOLS,
            'VITE_ENABLE_MOCK': import.meta.env.VITE_ENABLE_MOCK,
        };

        // 测试工具函数
        const utilFunctions = {
            'getJumpURL()': getJumpURL(),
            'getBaseURL()': getBaseURL(),
            'getApiURL()': getApiURL(),
            'envConfig.jumpURL': envConfig.jumpURL,
            'envConfig.baseURL': envConfig.baseURL,
            'envConfig.apiURL': envConfig.apiURL,
        };

        const envList = document.getElementById('env-list');

        // 显示原始环境变量
        const rawSection = document.createElement('div');
        rawSection.innerHTML = '<h2>Raw Environment Variables</h2>';
        envList.appendChild(rawSection);

        Object.entries(envVars).forEach(([name, value]) => {
            const item = document.createElement('div');
            item.className = 'env-item';
            const isEmpty = !value || value === '';
            item.innerHTML = `
                <span class="env-name">${name}:</span>
                <span class="env-value ${isEmpty ? 'empty' : 'success'}">${value || '(empty)'}</span>
            `;
            envList.appendChild(item);
        });

        // 显示工具函数结果
        const utilSection = document.createElement('div');
        utilSection.innerHTML = '<h2>Utility Functions</h2>';
        envList.appendChild(utilSection);

        Object.entries(utilFunctions).forEach(([name, value]) => {
            const item = document.createElement('div');
            item.className = 'env-item';
            const isEmpty = !value || value === '';
            item.innerHTML = `
                <span class="env-name">${name}:</span>
                <span class="env-value ${isEmpty ? 'empty' : 'success'}">${value || '(empty)'}</span>
            `;
            envList.appendChild(item);
        });

        // 在控制台输出详细信息
        console.log('=== Environment Variables Test ===');
        console.log('Raw env vars:', envVars);
        console.log('Utility functions:', utilFunctions);
        console.log('Full envConfig:', envConfig);
        console.log('import.meta.env:', import.meta.env);
    </script>
</body>
</html>
